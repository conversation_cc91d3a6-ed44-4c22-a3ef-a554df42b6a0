/**
 * Utility functions for CalculatorSuites
 */

const calculatorUtils = {
  /**
   * Format currency with proper locale
   * @param {number} value - The value to format
   * @param {string} locale - The locale to use for formatting (default: 'en-IN')
   * @param {string} currency - The currency code (default: 'INR')
   * @returns {string} Formatted currency string
   */
  formatCurrency: function (value, locale = "en-IN", currency = "INR") {
    return new Intl.NumberFormat(locale, {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(value);
  },

  /**
   * Format number with compact notation
   * @param {number} value - The value to format
   * @param {string} locale - The locale to use for formatting (default: 'en-IN')
   * @returns {string} Formatted number string
   */
  formatCompactNumber: function (value, locale = "en-IN") {
    if (value >= 10000000) {
      return (value / 10000000).toFixed(2) + " Cr";
    } else if (value >= 100000) {
      return (value / 100000).toFixed(2) + " Lakh";
    } else if (value >= 1000) {
      return (value / 1000).toFixed(2) + " K";
    }
    return value.toFixed(2);
  },

  /**
   * Format percentage
   * @param {number} value - The value to format as percentage
   * @param {number} decimals - Number of decimal places (default: 2)
   * @returns {string} Formatted percentage string
   */
  formatPercentage: function (value, decimals = 2) {
    return value.toFixed(decimals) + "%";
  },

  /**
   * Validate numeric input
   * @param {string|number} value - The value to validate
   * @param {number} min - Minimum allowed value
   * @param {number} max - Maximum allowed value
   * @param {string} errorMessage - Custom error message
   * @returns {Object} Validation result with valid flag and message
   */
  validateNumericInput: function (value, min, max, errorMessage) {
    const numValue = parseFloat(value);
    if (
      isNaN(numValue) ||
      numValue < min ||
      (max !== undefined && numValue > max)
    ) {
      return {
        valid: false,
        message:
          errorMessage ||
          `Please enter a valid number between ${min} and ${max}`,
      };
    }
    return { valid: true };
  },

  /**
   * Show error message for form input
   * @param {HTMLElement} inputElement - The input element with error
   * @param {string} message - Error message to display
   */
  showError: function (inputElement, message) {
    // Clear previous errors
    const formGroup = inputElement.closest(".form-group");
    const existingError = formGroup.querySelector(".error-message");
    if (existingError) {
      existingError.remove();
    }

    // Add new error message
    const errorElement = document.createElement("div");
    errorElement.className = "error-message";
    errorElement.textContent = message;
    formGroup.appendChild(errorElement);

    // Focus on the input element
    inputElement.focus();
  },

  /**
   * Clear all error messages in a form
   * @param {HTMLElement} formElement - The form element
   */
  clearErrors: function (formElement) {
    const errorMessages = formElement.querySelectorAll(".error-message");
    errorMessages.forEach((error) => error.remove());
  },

  /**
   * Save form values to localStorage
   * @param {string} key - Storage key
   * @param {Object} values - Values to store
   */
  saveToLocalStorage: function (key, values) {
    try {
      localStorage.setItem(key, JSON.stringify(values));
    } catch (e) {
      console.warn("Failed to save to localStorage:", e);
    }
  },

  /**
   * Load form values from localStorage
   * @param {string} key - Storage key
   * @returns {Object|null} Stored values or null if not found
   */
  loadFromLocalStorage: function (key) {
    try {
      const savedValues = localStorage.getItem(key);
      return savedValues ? JSON.parse(savedValues) : null;
    } catch (e) {
      console.warn("Failed to load from localStorage:", e);
      return null;
    }
  },

  /**
   * Round number to specified decimal places
   * @param {number} value - Value to round
   * @param {number} decimals - Number of decimal places
   * @returns {number} Rounded value
   */
  round: function (value, decimals = 2) {
    return Number(Math.round(value + "e" + decimals) + "e-" + decimals);
  },

  /**
   * Check if element is in viewport
   * @param {HTMLElement} element - Element to check
   * @returns {boolean} True if element is in viewport
   */
  isInViewport: function (element) {
    const rect = element.getBoundingClientRect();
    return (
      rect.top >= 0 &&
      rect.left >= 0 &&
      rect.bottom <=
        (window.innerHeight || document.documentElement.clientHeight) &&
      rect.right <= (window.innerWidth || document.documentElement.clientWidth)
    );
  },
};

/**
 * Initialize mobile navigation with improved functionality
 */
function initMobileNav() {
  const mobileMenuToggle = document.querySelector(".mobile-menu-toggle");
  const navMenu = document.querySelector(".nav-menu");
  const body = document.body;

  if (mobileMenuToggle && navMenu) {
    // Toggle mobile menu
    mobileMenuToggle.addEventListener("click", function () {
      navMenu.classList.toggle("active");
      mobileMenuToggle.classList.toggle("active");

      // Prevent body scrolling when menu is open
      if (navMenu.classList.contains("active")) {
        body.style.overflow = "hidden";
      } else {
        body.style.overflow = "";
      }
    });

    // Handle dropdown toggles on mobile
    const dropdownItems = document.querySelectorAll(".has-dropdown");
    dropdownItems.forEach((item) => {
      const link = item.querySelector(".nav-link");
      if (link) {
        link.addEventListener("click", function (e) {
          if (window.innerWidth < 768) {
            e.preventDefault();

            // Close other open dropdowns
            dropdownItems.forEach((otherItem) => {
              if (
                otherItem !== item &&
                otherItem.classList.contains("active")
              ) {
                otherItem.classList.remove("active");
              }
            });

            item.classList.toggle("active");
          }
        });
      }
    });

    // Close menu when clicking outside
    document.addEventListener("click", function (e) {
      if (
        navMenu.classList.contains("active") &&
        !navMenu.contains(e.target) &&
        !mobileMenuToggle.contains(e.target)
      ) {
        navMenu.classList.remove("active");
        mobileMenuToggle.classList.remove("active");
        body.style.overflow = "";
      }
    });

    // Close menu when window is resized to desktop size
    window.addEventListener("resize", function () {
      if (window.innerWidth >= 768 && navMenu.classList.contains("active")) {
        navMenu.classList.remove("active");
        mobileMenuToggle.classList.remove("active");
        body.style.overflow = "";
      }
    });
  }
}

/**
 * Initialize lazy loading for images
 */
function initLazyLoading() {
  if ("IntersectionObserver" in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const img = entry.target;
          const src = img.getAttribute("data-src");

          if (src) {
            img.src = src;
            img.removeAttribute("data-src");
          }

          observer.unobserve(img);
        }
      });
    });

    // Target all images with data-src attribute
    const lazyImages = document.querySelectorAll("img[data-src]");
    lazyImages.forEach((img) => {
      imageObserver.observe(img);
    });
  } else {
    // Fallback for browsers that don't support Intersection Observer
    const lazyImages = document.querySelectorAll("img[data-src]");
    lazyImages.forEach((img) => {
      img.src = img.getAttribute("data-src");
      img.removeAttribute("data-src");
    });
  }
}

/**
 * Initialize share buttons
 */
function initShareButtons() {
  const shareButtons = document.querySelectorAll(".share-results-btn");
  shareButtons.forEach((button) => {
    button.addEventListener("click", function () {
      // This will be handled by the main.js initShareFunctionality
      // Just ensure the button has the correct class
      if (!button.classList.contains("share-results-btn")) {
        button.classList.add("share-results-btn");
      }
    });
  });
}

/**
 * Initialize scroll to top button
 */
function initScrollToTop() {
  // Create the button element if it doesn't exist
  if (!document.querySelector(".scroll-to-top")) {
    const scrollBtn = document.createElement("button");
    scrollBtn.className = "scroll-to-top";
    scrollBtn.setAttribute("aria-label", "Scroll to top");
    scrollBtn.innerHTML = "&uarr;";
    document.body.appendChild(scrollBtn);
  }

  const scrollBtn = document.querySelector(".scroll-to-top");

  // Show/hide the button based on scroll position
  window.addEventListener("scroll", function () {
    if (window.pageYOffset > 300) {
      scrollBtn.style.display = "block";
    } else {
      scrollBtn.style.display = "none";
    }
  });

  // Scroll to top when clicked
  scrollBtn.addEventListener("click", function () {
    window.scrollTo({
      top: 0,
      behavior: "smooth",
    });
  });
}

/**
 * Storage Manager for calculator data
 * Provides a consistent interface for saving/loading calculator values and history
 */
window.storageManager = {
  /**
   * Load calculator form values from localStorage
   * @param {string} calculatorType - Type of calculator (e.g., 'gst', 'emi', 'bmi')
   * @returns {Object|null} Saved values or null if not found
   */
  loadCalculatorValues: function (calculatorType) {
    const key = `calculator_values_${calculatorType}`;
    return calculatorUtils.loadFromLocalStorage(key);
  },

  /**
   * Save calculator form values to localStorage
   * @param {string} calculatorType - Type of calculator (e.g., 'gst', 'emi', 'bmi')
   * @param {Object} values - Values to save
   */
  saveCalculatorValues: function (calculatorType, values) {
    const key = `calculator_values_${calculatorType}`;
    calculatorUtils.saveToLocalStorage(key, values);
  },

  /**
   * Save calculation result to history
   * @param {string} calculatorType - Type of calculator (e.g., 'gst', 'emi', 'bmi')
   * @param {Object} calculation - Calculation result to save
   */
  saveCalculationHistory: function (calculatorType, calculation) {
    const key = `calculator_history_${calculatorType}`;

    // Get existing history
    let history = calculatorUtils.loadFromLocalStorage(key) || [];

    // Add timestamp to calculation
    calculation.timestamp = new Date().toISOString();

    // Add new calculation to beginning of array
    history.unshift(calculation);

    // Keep only last 10 calculations
    if (history.length > 10) {
      history = history.slice(0, 10);
    }

    // Save updated history
    calculatorUtils.saveToLocalStorage(key, history);
  },

  /**
   * Get calculator form values (alias for loadCalculatorValues)
   * @param {string} calculatorType - Type of calculator
   * @returns {Object|null} Saved values or null if not found
   */
  getCalculatorValues: function (calculatorType) {
    return this.loadCalculatorValues(calculatorType);
  },

  /**
   * Get calculation history for a calculator
   * @param {string} calculatorType - Type of calculator
   * @returns {Array} Array of calculation history
   */
  getCalculationHistory: function (calculatorType) {
    const key = `calculator_history_${calculatorType}`;
    return calculatorUtils.loadFromLocalStorage(key) || [];
  },

  /**
   * Clear all data for a specific calculator
   * @param {string} calculatorType - Type of calculator
   */
  clearCalculatorData: function (calculatorType) {
    const valuesKey = `calculator_values_${calculatorType}`;
    const historyKey = `calculator_history_${calculatorType}`;

    try {
      localStorage.removeItem(valuesKey);
      localStorage.removeItem(historyKey);
    } catch (e) {
      console.warn("Failed to clear calculator data:", e);
    }
  },
};

// Initialize when DOM is loaded
document.addEventListener("DOMContentLoaded", function () {
  initMobileNav();
  initLazyLoading();
  initShareButtons();
  initScrollToTop();
});
